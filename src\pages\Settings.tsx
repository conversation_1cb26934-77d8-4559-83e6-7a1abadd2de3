import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import RolePermissionsTable from '@/components/settings/RolePermissionsTable';

interface Role {
  id: string;
  name: string;
  description: string;
}

interface RolePermission {
  id: string;
  role_id: string;
  module: string;
  can_view: boolean;
  can_add: boolean;
  can_edit: boolean;
  can_delete: boolean;
}

const Settings: React.FC = () => {
  const { userRole } = useAuth();
  const [settings, setSettings] = useState({
    smtp_host: '',
    smtp_port: 587,
    smtp_username: '',
    smtp_password: '',
    default_email_from: '',
    sendgrid_api_key: '',
    sendgrid_sender_email: '',
    twilio_account_sid: '',
    twilio_auth_token: '',
    twilio_phone_number: '',
    twilio_whatsapp_from: '',
    email_enabled: true,
    sms_enabled: false,
    whatsapp_enabled: false,
    alert_days_before: 7,
    notification_time: '09:00',
    tds_percentage: 10.0,
    default_commission: 0.0,
  });
  
  const [loading, setLoading] = useState(false);
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<RolePermission[]>([]);
  const [isAdmin, setIsAdmin] = useState(false);

  const modules = ['clients', 'investments', 'schemes', 'transactions', 'reports', 'calculator'];

  useEffect(() => {
    checkAdminStatus();
    if (userRole === 'Admin') {
      fetchSettings();
      fetchRoles();
      fetchPermissions();
    }
  }, [userRole]);

  const checkAdminStatus = async () => {
    try {
      const { data, error } = await supabase.rpc('is_admin');
      if (error) throw error;
      setIsAdmin(data || false);
    } catch (error) {
      console.error('Error checking admin status:', error);
      setIsAdmin(false);
    }
  };

  const fetchSettings = async () => {
    try {
      // First, clean up duplicate rows - keep only the first one
      const { data: allSettings } = await supabase
        .from('notification_settings')
        .select('*')
        .order('created_at', { ascending: true });

      if (allSettings && allSettings.length > 1) {
        // Delete all except the first one
        const idsToDelete = allSettings.slice(1).map(s => s.id);
        await supabase
          .from('notification_settings')
          .delete()
          .in('id', idsToDelete);
      }

      // Now fetch the single settings row
      const { data, error } = await supabase
        .from('notification_settings')
        .select('*')
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') {
        // If no settings exist, create default ones
        const defaultSettings = {
          email_enabled: true,
          sms_enabled: false,
          whatsapp_enabled: false,
          alert_days_before: 7,
          notification_time: '09:00',
          tds_percentage: 10.0,
          default_commission: 0.0,
          smtp_host: '',
          smtp_port: 587,
          smtp_username: '',
          smtp_password: '',
          default_email_from: '',
          sendgrid_api_key: '',
          sendgrid_sender_email: '',
          twilio_account_sid: '',
          twilio_auth_token: '',
          twilio_phone_number: '',
          twilio_whatsapp_from: ''
        };
        
        const { data: newSettings } = await supabase
          .from('notification_settings')
          .insert(defaultSettings)
          .select()
          .single();
          
        if (newSettings) {
          setSettings(newSettings);
        }
        return;
      }
      
      if (data) {
        setSettings(data);
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
    }
  };

  const fetchRoles = async () => {
    try {
      const { data, error } = await supabase
        .from('roles')
        .select('*')
        .order('name');

      if (error) throw error;
      setRoles(data || []);
    } catch (error) {
      console.error('Error fetching roles:', error);
    }
  };

  const fetchPermissions = async () => {
    try {
      const { data, error } = await supabase
        .from('role_permissions')
        .select('*');

      if (error) throw error;
      setPermissions(data || []);
    } catch (error) {
      console.error('Error fetching permissions:', error);
    }
  };

  const saveSettings = async () => {
    setLoading(true);
    try {
      // Check if settings record exists
      const { data: existingSettings } = await supabase
        .from('notification_settings')
        .select('id')
        .limit(1)
        .single();

      if (existingSettings) {
        // Update existing record
        const { error } = await supabase
          .from('notification_settings')
          .update(settings)
          .eq('id', existingSettings.id);

        if (error) throw error;
      } else {
        // Insert new record
        const { error } = await supabase
          .from('notification_settings')
          .insert(settings);

        if (error) throw error;
      }

      toast({
        title: "Success",
        description: "Settings saved successfully",
      });
      
      // Refresh settings to get updated data
      await fetchSettings();
    } catch (error) {
      console.error('Error saving settings:', error);
      toast({
        title: "Error",
        description: "Failed to save settings",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const updatePermission = async (roleId: string, module: string, permissionType: string, value: boolean) => {
    try {
      const existingPermission = permissions.find(p => p.role_id === roleId && p.module === module);
      
      if (existingPermission) {
        const { error } = await supabase
          .from('role_permissions')
          .update({ [permissionType]: value })
          .eq('id', existingPermission.id);

        if (error) throw error;
      } else {
        const { error } = await supabase
          .from('role_permissions')
          .insert({
            role_id: roleId,
            module: module,
            [permissionType]: value,
            can_view: permissionType === 'can_view' ? value : false,
            can_add: permissionType === 'can_add' ? value : false,
            can_edit: permissionType === 'can_edit' ? value : false,
            can_delete: permissionType === 'can_delete' ? value : false,
          });

        if (error) throw error;
      }

      await fetchPermissions();
      
      toast({
        title: "Success",
        description: "Permission updated successfully",
      });
    } catch (error) {
      console.error('Error updating permission:', error);
      toast({
        title: "Error",
        description: "Failed to update permission",
        variant: "destructive",
      });
    }
  };

  if (!isAdmin) {
    return (
      <div className="space-y-6">
        <h1 className="text-3xl font-bold">Settings</h1>
        <Card>
          <CardContent className="p-6">
            <p className="text-center text-gray-600">Access denied. Admin privileges required.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">Settings</h1>

      <Tabs defaultValue="notifications" className="w-full">
        <div className="w-full overflow-x-auto">
          <TabsList className="inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground min-w-max">
            <TabsTrigger value="notifications" className="whitespace-nowrap px-3 py-1.5 text-sm">Notifications</TabsTrigger>
            {/* <TabsTrigger value="smtp" className="whitespace-nowrap px-3 py-1.5 text-sm">SMTP Config</TabsTrigger> */}
            <TabsTrigger value="thirdparty" className="whitespace-nowrap px-3 py-1.5 text-sm">Third Party Config</TabsTrigger>
            <TabsTrigger value="financial" className="whitespace-nowrap px-3 py-1.5 text-sm">Financial</TabsTrigger>
            <TabsTrigger value="permissions" className="whitespace-nowrap px-3 py-1.5 text-sm">Role Permissions</TabsTrigger>
            <TabsTrigger value="general" className="whitespace-nowrap px-3 py-1.5 text-sm">General</TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="notifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Notification Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label>Email Notifications</Label>
                  <p className="text-sm text-gray-600">Send notifications via email</p>
                </div>
                <Switch
                  checked={settings.email_enabled}
                  onCheckedChange={(checked) => setSettings({ ...settings, email_enabled: checked })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label>SMS Notifications</Label>
                  <p className="text-sm text-gray-600">Send notifications via SMS</p>
                </div>
                <Switch
                  checked={settings.sms_enabled}
                  onCheckedChange={(checked) => setSettings({ ...settings, sms_enabled: checked })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label>WhatsApp Notifications</Label>
                  <p className="text-sm text-gray-600">Send notifications via WhatsApp</p>
                </div>
                <Switch
                  checked={settings.whatsapp_enabled}
                  onCheckedChange={(checked) => setSettings({ ...settings, whatsapp_enabled: checked })}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Alert Days Before Maturity</Label>
                  <Input
                    type="number"
                    value={settings.alert_days_before}
                    onChange={(e) => setSettings({ ...settings, alert_days_before: parseInt(e.target.value) })}
                  />
                </div>
                {/* <div>
                  <Label>Notification Time</Label>
                  <Input
                    type="time"
                    value={settings.notification_time}
                    onChange={(e) => setSettings({ ...settings, notification_time: e.target.value })}
                    
                  />
                </div> */}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* <TabsContent value="smtp" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>SMTP Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>SMTP Host</Label>
                  <Input
                    value={settings.smtp_host}
                    onChange={(e) => setSettings({ ...settings, smtp_host: e.target.value })}
                    placeholder="smtp.gmail.com"
                  />
                </div>
                <div>
                  <Label>SMTP Port</Label>
                  <Input
                    type="number"
                    value={settings.smtp_port}
                    onChange={(e) => setSettings({ ...settings, smtp_port: parseInt(e.target.value) })}
                  />
                </div>
              </div>

              <div>
                <Label>SMTP Username</Label>
                <Input
                  value={settings.smtp_username}
                  onChange={(e) => setSettings({ ...settings, smtp_username: e.target.value })}
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <Label>SMTP Password</Label>
                <Input
                  type="password"
                  value={settings.smtp_password}
                  onChange={(e) => setSettings({ ...settings, smtp_password: e.target.value })}
                  placeholder="App password"
                />
              </div>

              <div>
                <Label>Default From Email</Label>
                <Input
                  value={settings.default_email_from}
                  onChange={(e) => setSettings({ ...settings, default_email_from: e.target.value })}
                  placeholder="<EMAIL>"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent> */}

        <TabsContent value="thirdparty" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Email Service (SendGrid)</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>SendGrid API Key</Label>
                <Input
                  type="password"
                  value={settings.sendgrid_api_key}
                  onChange={(e) => setSettings({ ...settings, sendgrid_api_key: e.target.value })}
                  placeholder="SG.your_sendgrid_api_key"
                />
              </div>
              <div>
                <Label>SendGrid Sender Email</Label>
                <Input
                  value={settings.sendgrid_sender_email}
                  onChange={(e) => setSettings({ ...settings, sendgrid_sender_email: e.target.value })}
                  placeholder="<EMAIL>"
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>SMS Service (Twilio)</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Twilio Account SID</Label>
                <Input
                  value={settings.twilio_account_sid}
                  onChange={(e) => setSettings({ ...settings, twilio_account_sid: e.target.value })}
                  placeholder="ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                />
              </div>
              <div>
                <Label>Twilio Auth Token</Label>
                <Input
                  type="password"
                  value={settings.twilio_auth_token}
                  onChange={(e) => setSettings({ ...settings, twilio_auth_token: e.target.value })}
                  placeholder="your_auth_token"
                />
              </div>
              <div>
                <Label>Twilio Phone Number</Label>
                <Input
                  value={settings.twilio_phone_number}
                  onChange={(e) => setSettings({ ...settings, twilio_phone_number: e.target.value })}
                  placeholder="+**********"
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>WhatsApp Service (Twilio)</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Twilio WhatsApp From</Label>
                <Input
                  value={settings.twilio_whatsapp_from}
                  onChange={(e) => setSettings({ ...settings, twilio_whatsapp_from: e.target.value })}
                  placeholder="whatsapp:+14155238886"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="financial" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Financial Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>TDS Percentage (%)</Label>
                  <Input
                    type="number"
                    step="0.01"
                    value={settings.tds_percentage}
                    onChange={(e) => setSettings({ ...settings, tds_percentage: parseFloat(e.target.value) })}
                  />
                </div>
                <div>
                  <Label>Default Commission (%)</Label>
                  <Input
                    type="number"
                    step="0.01"
                    value={settings.default_commission}
                    onChange={(e) => setSettings({ ...settings, default_commission: parseFloat(e.target.value) })}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="permissions" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            {roles.map((role) => (
              <Card key={role.id}>
                <CardHeader>
                  <CardTitle className="text-lg">{role.name}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {modules.map((module) => {
                    const permission = permissions.find(p => p.role_id === role.id && p.module === module);
                    return (
                      <div key={module} className="p-3 bg-gray-50 rounded-lg">
                        <h4 className="font-medium capitalize mb-2">{module}</h4>
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          {['can_view', 'can_add', 'can_edit', 'can_delete'].map((perm) => (
                            <div key={perm} className="flex items-center justify-between">
                              <span className="capitalize">{perm.replace('can_', '')}</span>
                              <Switch
                                checked={Boolean(permission?.[perm as keyof RolePermission])}
                                onCheckedChange={(checked) => updatePermission(role.id, module, perm, checked)}
                            
                              />
                            </div>
                          ))}
                        </div>
                      </div>
                    );
                  })}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="general" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>General Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Additional general settings will be available here.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="flex justify-end">
        <Button onClick={saveSettings} disabled={loading}>
          {loading ? 'Saving...' : 'Save Settings'}
        </Button>
      </div>
    </div>
  );
};

export default Settings;
