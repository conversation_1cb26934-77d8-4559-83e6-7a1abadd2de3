
import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Search, Plus, Eye, Edit, ArrowUpDown, TrendingUp, TrendingDown, IndianRupee, Activity } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { EnhancedDateFilter, DateRange, getDateRangeValues } from '@/components/ui/enhanced-date-filter';
import { debounce } from 'lodash';
import { formatDisplayDate } from '@/utils/dateFormat';

// Utility function to format amount in K, L, Cr format
const formatAmountInIndianFormat = (amount: number): string => {
  if (amount >= 10000000) { // 1 Crore
    return `₹${(amount / 10000000).toFixed(1)}Cr`;
  } else if (amount >= 100000) { // 1 Lakh
    return `₹${(amount / 100000).toFixed(1)}L`;
  } else if (amount >= 1000) { // 1 Thousand
    return `₹${(amount / 1000).toFixed(1)}K`;
  } else {
    return `₹${amount.toLocaleString()}`;
  }
};

interface Transaction {
  id: string;
  investment_id: string;
  amount_type: string;
  amount: number;
  transaction_date: string;
  reference_number: string;
  remark: string;
  created_at: string;
  investments?: {
    scheme_name: string;
    scheme_code: string;
    primary_applicant_cif_id: string | null;
    secondary_applicant_cif_id: string | null;
    clients: {
      first_name: string;
      last_name: string;
      mobile_number: string;
      village: string | null;
    };
    secondary_applicant?: {
      first_name: string;
      last_name: string;
      mobile_number: string;
      village: string | null;
    } | null;
  } | null;
}

interface TransactionMetrics {
  totalTransactions: number;
  creditAmount: number;
  debitAmount: number;
  totalInvestments: number;
  totalPayouts: number;
}

const Transactions: React.FC = () => {
  // Function to get color for amount type
  const getAmountTypeColor = (type: string) => {
    switch (type) {
      case 'investment': return 'bg-green-100 text-green-800 border-green-200';
      case 'reinvestment': return 'bg-green-100 text-green-800 border-green-200';
      case 'interest_payout': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'withdrawal': return 'bg-red-100 text-red-800 border-red-200';
      case 'penalty': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'commission': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [metrics, setMetrics] = useState<TransactionMetrics>({
    totalTransactions: 0,
    creditAmount: 0,
    debitAmount: 0,
    totalInvestments: 0,
    totalPayouts: 0
  });
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState(searchParams.get('search') || '');
  const [searchInput, setSearchInput] = useState(searchParams.get('search') || '');
  const [amountTypeFilter, setAmountTypeFilter] = useState(searchParams.get('amountType') || 'all');
  const [dateFilter, setDateFilter] = useState(searchParams.get('date') || 'all');
  const [customDateRange, setCustomDateRange] = useState<DateRange>();
  const [sortBy, setSortBy] = useState(searchParams.get('sortBy') || 'created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>((searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc');

  // Pagination
  const [itemsPerPage, setItemsPerPage] = useState(parseInt(searchParams.get('limit') || '10'));
  const currentPage = parseInt(searchParams.get('page') || '1');
  const [totalCount, setTotalCount] = useState(0);
  const totalPages = Math.ceil(totalCount / itemsPerPage);

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      setSearchTerm(value);
    }, 500),
    []
  );

  // Handle search input change
  useEffect(() => {
    debouncedSearch(searchInput);
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchInput, debouncedSearch]);

  useEffect(() => {
    fetchTransactions();
  }, [currentPage, searchTerm, amountTypeFilter, dateFilter, customDateRange, sortBy, sortOrder, itemsPerPage]);

  useEffect(() => {
    fetchMetrics();
  }, [amountTypeFilter, dateFilter, customDateRange]);

  useEffect(() => {
    // Update URL with current filters
    const params = new URLSearchParams();
    if (searchTerm) params.set('search', searchTerm);
    if (amountTypeFilter !== 'all') params.set('amountType', amountTypeFilter);
    if (dateFilter !== 'all') params.set('date', dateFilter);
    if (sortBy !== 'created_at') params.set('sortBy', sortBy);
    if (sortOrder !== 'desc') params.set('sortOrder', sortOrder);
    if (currentPage !== 1) params.set('page', currentPage.toString());
    if (itemsPerPage !== 10) params.set('limit', itemsPerPage.toString());

    setSearchParams(params);
  }, [searchTerm, amountTypeFilter, dateFilter, sortBy, sortOrder, currentPage, itemsPerPage, setSearchParams]);

  const fetchMetrics = async () => {
    try {
      let query = supabase
        .from('transactions')
        .select('amount, amount_type');

      // Apply date filter to metrics
      const dateRange = getDateRangeValues(dateFilter, customDateRange);
      if (dateRange) {
        query = query.gte('transaction_date', dateRange.from)
          .lte('transaction_date', dateRange.to);
      }

      const { data, error } = await query;

      if (error) throw error;

      const metricsData = data?.reduce((acc, transaction) => {
        acc.totalTransactions += 1;

        if (['investment', 'reinvestment'].includes(transaction.amount_type)) {
          acc.creditAmount += transaction.amount;
          acc.totalInvestments += 1;
        } else {
          acc.debitAmount += transaction.amount;
          if (['interest_payout', 'withdrawal'].includes(transaction.amount_type)) {
            acc.totalPayouts += 1;
          }
        }

        return acc;
      }, {
        totalTransactions: 0,
        creditAmount: 0,
        debitAmount: 0,
        totalInvestments: 0,
        totalPayouts: 0
      }) || metrics;

      setMetrics(metricsData);
    } catch (error) {
      console.error('Error fetching metrics:', error);
    }
  };

  const fetchTransactions = async () => {
    setLoading(true);
    try {
      let transactionIds: string[] = [];

      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase().trim();

        // Search in transactions table directly
        const { data: directTransactions } = await supabase
          .from('transactions')
          .select('id')
          .or(`reference_number.ilike.%${searchLower}%,amount_type.ilike.%${searchLower}%,remark.ilike.%${searchLower}%`);

        // Search through investments (scheme name/code)
        const { data: investmentIds } = await supabase
          .from('investments')
          .select('id')
          .or(`scheme_name.ilike.%${searchLower}%,scheme_code.ilike.%${searchLower}%`);

        const { data: investmentTransactions } = investmentIds?.length ? await supabase
          .from('transactions')
          .select('id')
          .in('investment_id', investmentIds.map(inv => inv.id)) : { data: [] };

        // Search through clients
        const { data: clientIds } = await supabase
          .from('clients')
          .select('id')
          .or(`first_name.ilike.%${searchLower}%,last_name.ilike.%${searchLower}%,mobile_number.ilike.%${searchLower}%`);

        const { data: clientInvestmentIds } = clientIds?.length ? await supabase
          .from('investments')
          .select('id')
          .in('client_id', clientIds.map(client => client.id)) : { data: [] };

        const { data: clientTransactions } = clientInvestmentIds?.length ? await supabase
          .from('transactions')
          .select('id')
          .in('investment_id', clientInvestmentIds.map(inv => inv.id)) : { data: [] };

        // Combine all transaction IDs
        const allTransactionIds = new Set([
          ...(directTransactions || []).map(t => t.id),
          ...(investmentTransactions || []).map(t => t.id),
          ...(clientTransactions || []).map(t => t.id)
        ]);

        transactionIds = Array.from(allTransactionIds);
      }

      let query = supabase
        .from('transactions')
        .select(`
          *,
          investments!transactions_investment_id_fkey (
            scheme_name,
            scheme_code,
            primary_applicant_cif_id,
            secondary_applicant_cif_id,
            clients!investments_client_id_fkey (
              first_name,
              last_name,
              mobile_number,
              village
            ),
            secondary_applicant:clients!investments_second_applicant_id_fkey (
              first_name,
              last_name,
              mobile_number,
              village
            )
          )
        `, { count: 'exact' });

      // Apply search filter
      if (searchTerm && transactionIds.length > 0) {
        query = query.in('id', transactionIds);
      } else if (searchTerm && transactionIds.length === 0) {
        // No results found, return empty
        setTransactions([]);
        setTotalCount(0);
        setLoading(false);
        return;
      }

      // Apply amount type filter
      if (amountTypeFilter !== 'all') {
        query = query.eq('amount_type', amountTypeFilter);
      }

      // Apply date filter
      const dateRange = getDateRangeValues(dateFilter, customDateRange);
      if (dateRange) {
        query = query.gte('transaction_date', dateRange.from)
          .lte('transaction_date', dateRange.to);
      }

      // Apply sorting
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      // Apply pagination
      const from = (currentPage - 1) * itemsPerPage;
      const to = from + itemsPerPage - 1;
      query = query.range(from, to);

      const { data, error, count } = await query;

      if (error) throw error;

      setTransactions(data || []);
      setTotalCount(count || 0);

    } catch (error) {
      console.error('Error fetching transactions:', error);
      toast({
        title: "Error",
        description: "Failed to fetch transactions",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('page', '1');
      return newParams;
    });
  };

  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages || page === currentPage) return;
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('page', page.toString());
      return newParams;
    });
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('limit', newItemsPerPage.toString());
      newParams.set('page', '1');
      return newParams;
    });
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const SortableHeader = ({ column, children }: { column: string; children: React.ReactNode }) => (
    <TableHead
      className="cursor-pointer hover:bg-gray-100 select-none font-semibold"
      onClick={() => handleSort(column)}
    >
      <div className="flex items-center gap-2">
        {children}
        <ArrowUpDown className="h-4 w-4" />
        {sortBy === column && (
          <span className="text-xs text-blue-600">
            {sortOrder === 'asc' ? '↑' : '↓'}
          </span>
        )}
      </div>
    </TableHead>
  );

  if (loading) {
    return <div className="flex items-center justify-center h-64">Loading transactions...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Transactions</h1>
        <Button onClick={() => navigate('/transactions/new')}>
          <Plus className="h-4 w-4 mr-2" />
          Add Transaction
        </Button>
      </div>

      {/* Metrics Cards */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2 md:gap-4">
        <Card
          className={`cursor-pointer hover:shadow-md transition-shadow ${amountTypeFilter === 'all' ? 'ring-2 ring-blue-500' : ''}`}
          onClick={() => setAmountTypeFilter('all')}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1 md:pb-2">
            <CardTitle className="text-xs md:text-sm font-medium truncate">Total Transactions</CardTitle>
            <Activity className="h-3 w-3 md:h-4 md:w-4 text-muted-foreground flex-shrink-0" />
          </CardHeader>
          <CardContent className="pt-1 md:pt-0">
            <div className="text-lg md:text-2xl font-bold">{metrics.totalTransactions}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1 md:pb-2">
            <CardTitle className="text-xs md:text-sm font-medium truncate">Credit Amount</CardTitle>
            <TrendingUp className="h-3 w-3 md:h-4 md:w-4 text-green-600 flex-shrink-0" />
          </CardHeader>
          <CardContent className="pt-1 md:pt-0">
            <div className="text-lg md:text-2xl font-bold text-green-600">{formatAmountInIndianFormat(metrics.creditAmount)}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1 md:pb-2">
            <CardTitle className="text-xs md:text-sm font-medium truncate">Debit Amount</CardTitle>
            <TrendingDown className="h-3 w-3 md:h-4 md:w-4 text-red-600 flex-shrink-0" />
          </CardHeader>
          <CardContent className="pt-1 md:pt-0">
            <div className="text-lg md:text-2xl font-bold text-red-600">{formatAmountInIndianFormat(metrics.debitAmount)}</div>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer hover:shadow-md transition-shadow ${amountTypeFilter === 'investment' ? 'ring-2 ring-blue-500' : ''}`}
          onClick={() => setAmountTypeFilter('investment')}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1 md:pb-2">
            <CardTitle className="text-xs md:text-sm font-medium truncate">Investments</CardTitle>
            <IndianRupee className="h-3 w-3 md:h-4 md:w-4 text-blue-600 flex-shrink-0" />
          </CardHeader>
          <CardContent className="pt-1 md:pt-0">
            <div className="text-lg md:text-2xl font-bold">{metrics.totalInvestments}</div>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer hover:shadow-md transition-shadow ${['interest_payout', 'withdrawal'].includes(amountTypeFilter) ? 'ring-2 ring-orange-500' : ''}`}
          onClick={() => setAmountTypeFilter('interest_payout')}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1 md:pb-2">
            <CardTitle className="text-xs md:text-sm font-medium truncate">Payouts</CardTitle>
            <Activity className="h-3 w-3 md:h-4 md:w-4 text-orange-600 flex-shrink-0" />
          </CardHeader>
          <CardContent className="pt-1 md:pt-0">
            <div className="text-lg md:text-2xl font-bold">{metrics.totalPayouts}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col gap-4">

        <div className="flex flex-col lg:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search by reference, client name, scheme, or mobile..."
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={amountTypeFilter} onValueChange={setAmountTypeFilter}>
            <SelectTrigger className="w-full lg:w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="investment">Investment</SelectItem>
              <SelectItem value="interest_payout">Interest Payout</SelectItem>
              <SelectItem value="withdrawal">Withdrawal</SelectItem>
              <SelectItem value="reinvestment">Reinvestment</SelectItem>
              <SelectItem value="penalty">Penalty</SelectItem>
              <SelectItem value="commission">Commission</SelectItem>
            </SelectContent>
          </Select>
          <div className="w-full lg:w-auto">

            <EnhancedDateFilter
              value={dateFilter}
              onChange={setDateFilter}
              customRange={customDateRange}
              onCustomRangeChange={setCustomDateRange}
            />
          </div>
        </div>
      </div>

      {/* Transactions Table */}
      <Card>
        <CardHeader>
          <CardTitle>Transactions ({totalCount} total)</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {transactions.length > 0 ? (
            <>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-gray-50">
                      <TableHead className="w-16 text-center font-semibold">S.No</TableHead>
                      <SortableHeader column="investments.clients.first_name">Client</SortableHeader>
                      <SortableHeader column="investments.scheme_name">Investment</SortableHeader>
                      <SortableHeader column="reference_number">Reference</SortableHeader>
                      <SortableHeader column="amount_type">Type</SortableHeader>
                      <SortableHeader column="amount">Amount</SortableHeader>
                      <SortableHeader column="transaction_date">Date</SortableHeader>
                      <TableHead className="text-right font-semibold">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {transactions.map((transaction, index) => (
                      <TableRow key={transaction.id} className="hover:bg-gray-50">
                        <TableCell className="w-16 text-center font-medium">
                          <span className="text-sm font-bold text-blue-600">{(currentPage - 1) * itemsPerPage + index + 1}</span>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-0.5">
                            <div className="flex items-center gap-1">
                              <span className="font-medium text-gray-900">
                                {transaction.investments?.clients?.first_name} {transaction.investments?.clients?.last_name}
                              </span>
                              {transaction.investments?.primary_applicant_cif_id && (
                                <span className="text-xs px-1 py-0.5 bg-blue-50 text-blue-700 rounded">
                                  {transaction.investments.primary_applicant_cif_id}
                                </span>
                              )}
                            </div>
                            <div className="flex flex-wrap items-center gap-x-2 text-xs text-gray-500">
                              <span>📱 {transaction.investments?.clients?.mobile_number}</span>
                              {transaction.investments?.clients?.village && (
                                <span>📍 {transaction.investments.clients.village}</span>
                              )}
                            </div>
                            
                            {/* Show secondary applicant if available */}
                            {transaction.investments?.secondary_applicant && (
                              <div className="mt-1 pt-1 border-t border-gray-100">
                                <div className="flex items-center gap-1">
                                  <span className="text-xs text-gray-500">+</span>
                                  <span className="text-xs font-medium">
                                    {transaction.investments.secondary_applicant.first_name} {transaction.investments.secondary_applicant.last_name}
                                  </span>
                                  {transaction.investments.secondary_applicant_cif_id && (
                                    <span className="text-xs px-1 py-0.5 bg-gray-50 text-gray-500 rounded">
                                      {transaction.investments.secondary_applicant_cif_id}
                                    </span>
                                  )}
                                </div>
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{transaction.investments?.scheme_name}</div>
                            <div className="text-sm text-gray-500">{transaction.investments?.scheme_code}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">{transaction.reference_number}</div>
                          {transaction.remark && (
                            <div className="text-sm text-gray-500">{transaction.remark}</div>
                          )}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className={`capitalize ${getAmountTypeColor(transaction.amount_type)}`}>
                            {transaction.amount_type.replace(/_/g, ' ')}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className={`font-medium ${['investment', 'reinvestment'].includes(transaction.amount_type)
                            ? 'text-green-600'
                            : 'text-red-600'
                            }`}>
                            {['investment', 'reinvestment'].includes(transaction.amount_type) ? '+' : '-'}{formatAmountInIndianFormat(transaction.amount)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {formatDisplayDate(transaction.transaction_date)}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => navigate(`/transactions/${transaction.id}`)}
                              className="h-8 w-8 p-0"
                              title="View Details"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => navigate(`/transactions/${transaction.id}/edit`)}
                              className="h-8 w-8 p-0"
                              title="Edit Transaction"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Enhanced Responsive Pagination */}
              {totalPages > 1 && (
                <div className="p-4 border-t bg-gray-50">
                  {/* Mobile Pagination */}
                  <div className="flex flex-col space-y-4 sm:hidden">
                    <div className="text-sm text-gray-600 text-center">
                      Page {currentPage} of {totalPages} ({totalCount} total results)
                    </div>
                    <div className="flex justify-between items-center">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="flex items-center gap-2 min-w-[80px]"
                      >
                        <span className="text-xs">‹</span>
                        Previous
                      </Button>

                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-500">Page</span>
                        <select
                          value={currentPage}
                          onChange={(e) => handlePageChange(Number(e.target.value))}
                          className="px-3 py-1 border border-gray-300 rounded-md text-sm bg-white min-w-[60px] text-center"
                        >
                          {Array.from({ length: totalPages }, (_, i) => (
                            <option key={i + 1} value={i + 1}>{i + 1}</option>
                          ))}
                        </select>
                        <span className="text-sm text-gray-500">of {totalPages}</span>
                      </div>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className="flex items-center gap-2 min-w-[80px]"
                      >
                        Next
                        <span className="text-xs">›</span>
                      </Button>
                    </div>
                  </div>

                  {/* Desktop Pagination */}
                  <div className="hidden sm:flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                    <div className="text-sm text-gray-600 order-2 lg:order-1">
                      Showing <span className="font-medium">{((currentPage - 1) * itemsPerPage) + 1}</span> to <span className="font-medium">{Math.min(currentPage * itemsPerPage, totalCount)}</span> of <span className="font-medium">{totalCount}</span> results
                    </div>

                    <div className="flex items-center justify-center lg:justify-end gap-2 order-1 lg:order-2">
                      {/* First Page Button */}
                      {currentPage > 3 && totalPages > 7 && (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePageChange(1)}
                            className="w-10 h-10 p-0"
                          >
                            1
                          </Button>
                          {currentPage > 4 && (
                            <span className="px-2 text-gray-400">...</span>
                          )}
                        </>
                      )}

                      {/* Previous Button */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="flex items-center gap-1 px-3"
                      >
                        <span className="text-sm">‹</span>
                        <span className="hidden md:inline">Previous</span>
                      </Button>

                      {/* Page Numbers */}
                      <div className="flex items-center gap-1">
                        {Array.from({ length: Math.min(totalPages, 7) }, (_, i) => {
                          let page: number;

                          if (totalPages <= 7) {
                            page = i + 1;
                          } else if (currentPage <= 4) {
                            page = i + 1;
                          } else if (currentPage >= totalPages - 3) {
                            page = totalPages - 6 + i;
                          } else {
                            page = currentPage - 3 + i;
                          }

                          // Don't show if it's already covered by first/last
                          if ((currentPage > 3 && totalPages > 7 && page === 1) ||
                            (currentPage < totalPages - 2 && totalPages > 7 && page === totalPages)) {
                            return null;
                          }

                          return (
                            <Button
                              key={page}
                              variant={currentPage === page ? "default" : "outline"}
                              size="sm"
                              onClick={() => handlePageChange(page)}
                              className={`w-10 h-10 p-0 ${currentPage === page
                                ? 'bg-blue-600 text-white hover:bg-blue-700'
                                : 'hover:bg-gray-100'
                                }`}
                            >
                              {page}
                            </Button>
                          );
                        })}
                      </div>

                      {/* Next Button */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className="flex items-center gap-1 px-3"
                      >
                        <span className="hidden md:inline">Next</span>
                        <span className="text-sm">›</span>
                      </Button>

                      {/* Last Page Button */}
                      {currentPage < totalPages - 2 && totalPages > 7 && (
                        <>
                          {currentPage < totalPages - 3 && (
                            <span className="px-2 text-gray-400">...</span>
                          )}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePageChange(totalPages)}
                            className="w-10 h-10 p-0"
                          >
                            {totalPages}
                          </Button>
                        </>
                      )}
                    </div>
                  </div>

                  {/* Items per page selector for larger screens */}
                  <div className="hidden xl:flex items-center justify-center mt-4 pt-4 border-t border-gray-200">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <span>Show</span>
                      <select
                        value={itemsPerPage}
                        onChange={(e) => handleItemsPerPageChange(Number(e.target.value))}
                        className="px-2 py-1 border border-gray-300 rounded text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="10">10</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                      </select>
                      <span>entries per page</span>
                    </div>
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-12">
              <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No transactions found</h3>
              <p className="text-gray-500 mb-4">
                {searchTerm || amountTypeFilter !== 'all' || dateFilter !== 'all'
                  ? 'Try adjusting your search or filters'
                  : 'Get started by adding your first transaction'
                }
              </p>
              {!searchTerm && amountTypeFilter === 'all' && dateFilter === 'all' && (
                <Button onClick={() => navigate('/transactions/new')}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Transaction
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Transactions;
