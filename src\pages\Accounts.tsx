import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Search, Plus, Eye, Edit, Trash2, CreditCard, IndianRupee, TrendingUp, Users, ArrowUpDown, ChevronLeft, ChevronRight, UserX } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { EnhancedDateFilter, DateRange, getDateRangeValues } from '@/components/ui/enhanced-date-filter';
import { formatDisplayDate } from '@/utils/dateFormat';

interface Account {
  id: string;
  sb_account_number: string;
  account_type: string;
  already_opened: boolean;
  opened_by_agency: boolean;
  opening_balance: number;
  status: string;
  opened_at: string;
  created_at: string;
  remarks: string;
  client_sb_accounts?: {
    clients: {
      first_name: string;
      last_name: string;
      mobile_number: string;
      email: string;
    };
  }[];
}

const Accounts: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState(searchParams.get('search') || '');
  const [statusFilter, setStatusFilter] = useState(searchParams.get('status') || 'all');
  const [typeFilter, setTypeFilter] = useState(searchParams.get('type') || 'all');
  const [dateFilter, setDateFilter] = useState(searchParams.get('date') || 'all');
  const [customDateRange, setCustomDateRange] = useState<DateRange>();
  const [sortBy, setSortBy] = useState(searchParams.get('sortBy') || 'created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>((searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc');

  // Pagination
  const itemsPerPage = 10;
  const currentPage = parseInt(searchParams.get('page') || '1');
  const [totalCount, setTotalCount] = useState(0);
  const totalPages = Math.ceil(totalCount / itemsPerPage);

  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    createdByAgency: 0,
    notCreatedByAgency: 0
  });

  useEffect(() => {
    fetchAccounts();
    fetchStats();
  }, [currentPage, searchTerm, statusFilter, typeFilter, dateFilter, customDateRange, sortBy, sortOrder]);

  useEffect(() => {
    // Update URL with current filters
    const params = new URLSearchParams();
    if (searchTerm) params.set('search', searchTerm);
    if (statusFilter !== 'all') params.set('status', statusFilter);
    if (typeFilter !== 'all') params.set('type', typeFilter);
    if (dateFilter !== 'all') params.set('date', dateFilter);
    if (sortBy !== 'created_at') params.set('sortBy', sortBy);
    if (sortOrder !== 'desc') params.set('sortOrder', sortOrder);
    if (currentPage !== 1) params.set('page', currentPage.toString());

    setSearchParams(params);
  }, [searchTerm, statusFilter, typeFilter, dateFilter, sortBy, sortOrder, currentPage]);

  const fetchStats = async () => {
    try {
      const { data, error } = await supabase
        .from('sb_accounts')
        .select('status, opened_by_agency')
        .eq('is_deleted', false);

      if (error) throw error;

      const totalCount = data?.length || 0;
      const activeCount = data?.filter(acc => acc.status === 'active').length || 0;
      const createdByAgencyCount = data?.filter(acc => acc.opened_by_agency === true).length || 0;
      const notCreatedByAgencyCount = data?.filter(acc => acc.opened_by_agency === false).length || 0;

      setStats({
        total: totalCount,
        active: activeCount,
        createdByAgency: createdByAgencyCount,
        notCreatedByAgency: notCreatedByAgencyCount
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const fetchAccounts = async () => {
    setLoading(true);
    try {
      let query = supabase
        .from('sb_accounts')
        .select(`
          *,
          client_sb_accounts!inner (
            clients (
              first_name,
              last_name,
              mobile_number,
              email
            )
          )
        `, { count: 'exact' })
        .eq('is_deleted', false);

      // Apply search filter - search accounts and related client data
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase().trim();

        // Get client IDs that match the search
        const { data: matchingClients } = await supabase
          .from('clients')
          .select('id')
          .or(`first_name.ilike.%${searchLower}%,last_name.ilike.%${searchLower}%,mobile_number.ilike.%${searchLower}%,email.ilike.%${searchLower}%`);

        const clientIds = matchingClients?.map(c => c.id) || [];

        // Get account IDs that have matching clients
        let accountIds: string[] = [];
        if (clientIds.length > 0) {
          const { data: matchingAccountRelations } = await supabase
            .from('client_sb_accounts')
            .select('sb_account_id')
            .in('client_id', clientIds);
          
          accountIds = matchingAccountRelations?.map(r => r.sb_account_id) || [];
        }

        // Build the search query
        if (accountIds.length > 0) {
          query = query.or(`sb_account_number.ilike.%${searchLower}%,remarks.ilike.%${searchLower}%,id.in.(${accountIds.join(',')})`); 
        } else {
          query = query.or(`sb_account_number.ilike.%${searchLower}%,remarks.ilike.%${searchLower}%`);
        }
      }

      // Apply status filter
      if (statusFilter !== 'all') {
        query = query.eq('status', statusFilter);
      }

      // Apply type filter
      if (typeFilter !== 'all') {
        if (typeFilter === 'agency') {
          query = query.eq('opened_by_agency', true);
        } else if (typeFilter === 'non-agency') {
          query = query.eq('opened_by_agency', false);
        } else {
          query = query.eq('account_type', typeFilter);
        }
      }

      // Apply date filter
      const dateRange = getDateRangeValues(dateFilter, customDateRange);
      if (dateRange) {
        query = query.gte('created_at', dateRange.from)
          .lte('created_at', dateRange.to);
      }

      // Apply sorting
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      // Apply pagination
      const from = (currentPage - 1) * itemsPerPage;
      const to = from + itemsPerPage - 1;
      query = query.range(from, to);

      const { data, error, count } = await query;

      if (error) throw error;

      setAccounts(data || []);
      setTotalCount(count || 0);

    } catch (error) {
      console.error('Error fetching accounts:', error);
      toast({
        title: "Error",
        description: "Failed to fetch accounts",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('page', '1');
      return newParams;
    });
  };

  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages || page === currentPage) return;
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('page', page.toString());
      return newParams;
    });
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleDelete = async (accountId: string) => {
    if (!confirm('Are you sure you want to delete this account?')) return;

    try {
      const { error } = await supabase
        .from('sb_accounts')
        .update({ is_deleted: true })
        .eq('id', accountId);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Account deleted successfully",
      });

      fetchAccounts();
    } catch (error) {
      console.error('Error deleting account:', error);
      toast({
        title: "Error",
        description: "Failed to delete account",
        variant: "destructive",
      });
    }
  };

  // Filter handlers for stats cards
  const handleStatsCardClick = (filterType: 'status' | 'type', filterValue: string) => {
    if (filterType === 'status') {
      setStatusFilter(filterValue);
    } else if (filterType === 'type') {
      setTypeFilter(filterValue);
    }
    // Reset to first page when applying filter
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('page', '1');
      return newParams;
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'suspended': return 'bg-red-100 text-red-800';
      case 'closed': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'savings': return 'bg-blue-100 text-blue-800';
      case 'current': return 'bg-purple-100 text-purple-800';
      case 'fixed_deposit': return 'bg-orange-100 text-orange-800';
      case 'recurring': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const SortableHeader = ({ column, children }: { column: string; children: React.ReactNode }) => (
    <TableHead
      className="cursor-pointer hover:bg-gray-100 select-none font-semibold"
      onClick={() => handleSort(column)}
    >
      <div className="flex items-center gap-2">
        {children}
        <ArrowUpDown className="h-4 w-4" />
        {sortBy === column && (
          <span className="text-xs text-blue-600">
            {sortOrder === 'asc' ? '↑' : '↓'}
          </span>
        )}
      </div>
    </TableHead>
  );

  if (loading) {
    return <div className="flex items-center justify-center h-64">Loading accounts...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Accounts</h1>
        <Button onClick={() => navigate('/accounts/new')}>
          <Plus className="h-4 w-4 mr-2" />
          Add Account
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-2 md:gap-4">
        <Card
          className={`cursor-pointer hover:shadow-md transition-shadow ${statusFilter === 'all' && typeFilter === 'all' ? 'ring-2 ring-blue-500' : ''}`}
          onClick={() => {
            setStatusFilter('all');
            setTypeFilter('all');
            setSearchParams(prev => {
              const newParams = new URLSearchParams(prev);
              newParams.delete('status');
              newParams.delete('type');
              newParams.set('page', '1');
              return newParams;
            });
          }}
        >
          <CardContent className="p-3 md:p-4">
            <div className="flex items-center gap-2 md:gap-3">
              <div className="p-1.5 md:p-2 bg-blue-100 rounded-lg">
                <CreditCard className="h-4 w-4 md:h-5 md:w-5 text-blue-600" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-xs md:text-sm text-gray-600 truncate">Total Accounts</p>
                <p className="text-lg md:text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer hover:shadow-md transition-shadow ${statusFilter === 'active' ? 'ring-2 ring-green-500' : ''}`}
          onClick={() => handleStatsCardClick('status', 'active')}
        >
          <CardContent className="p-3 md:p-4">
            <div className="flex items-center gap-2 md:gap-3">
              <div className="p-1.5 md:p-2 bg-green-100 rounded-lg">
                <TrendingUp className="h-4 w-4 md:h-5 md:w-5 text-green-600" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-xs md:text-sm text-gray-600 truncate">Active</p>
                <p className="text-lg md:text-2xl font-bold">{stats.active}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer hover:shadow-md transition-shadow ${typeFilter === 'agency' ? 'ring-2 ring-purple-500' : ''}`}
          onClick={() => handleStatsCardClick('type', 'agency')}
        >
          <CardContent className="p-3 md:p-4">
            <div className="flex items-center gap-2 md:gap-3">
              <div className="p-1.5 md:p-2 bg-purple-100 rounded-lg">
                <Users className="h-4 w-4 md:h-5 md:w-5 text-purple-600" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-xs md:text-sm text-gray-600 truncate">Created by Agency</p>
                <p className="text-lg md:text-2xl font-bold">{stats.createdByAgency}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer hover:shadow-md transition-shadow ${typeFilter === 'non-agency' ? 'ring-2 ring-orange-500' : ''}`}
          onClick={() => handleStatsCardClick('type', 'non-agency')}
        >
          <CardContent className="p-3 md:p-4">
            <div className="flex items-center gap-2 md:gap-3">
              <div className="p-1.5 md:p-2 bg-orange-100 rounded-lg">
                <UserX className="h-4 w-4 md:h-5 md:w-5 text-orange-600" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-xs md:text-sm text-gray-600 truncate">Not Created by Agency</p>
                <p className="text-lg md:text-2xl font-bold">{stats.notCreatedByAgency}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col gap-4">

        <div className="flex flex-col lg:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search by account number, remarks, client name..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full lg:w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
              <SelectItem value="suspended">Suspended</SelectItem>
              <SelectItem value="closed">Closed</SelectItem>
            </SelectContent>
          </Select>
          <Select value={typeFilter} onValueChange={setTypeFilter}>
            <SelectTrigger className="w-full lg:w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="single">Single</SelectItem>
              <SelectItem value="joint">Joint</SelectItem>
              <SelectItem value="agency">Created by Agency</SelectItem>
              <SelectItem value="non-agency">Not Created by Agency</SelectItem>
            </SelectContent>
          </Select>
          <div className="w-full lg:w-auto">

            <EnhancedDateFilter
              value={dateFilter}
              onChange={setDateFilter}
              customRange={customDateRange}
              onCustomRangeChange={setCustomDateRange}
            />
          </div>
        </div>
      </div>

      {/* Accounts List Table */}
      <Card>
        <CardHeader>
          <CardTitle>Accounts ({totalCount} total)</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {accounts.length > 0 ? (
            <>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-gray-50">
                      <TableHead className="w-16 text-center font-semibold">S.No</TableHead>
                      <SortableHeader column="client_sb_accounts.clients.first_name">Client</SortableHeader>
                      <SortableHeader column="sb_account_number">Account Number</SortableHeader>
                      <SortableHeader column="account_type">Type</SortableHeader>
                      <SortableHeader column="opened_by_agency">Created By</SortableHeader>
                      <SortableHeader column="opening_balance">Opening Balance</SortableHeader>
                      <SortableHeader column="status">Status</SortableHeader>
                      <SortableHeader column="created_at">Created Date</SortableHeader>
                      <TableHead className="text-right font-semibold">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {accounts.map((account, index) => (
                      <TableRow key={account.id} className="hover:bg-gray-50">
                        <TableCell className="w-16 text-center font-medium">
                          <span className="text-sm font-bold text-blue-600">{(currentPage - 1) * itemsPerPage + index + 1}</span>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium text-gray-900">
                              {account.client_sb_accounts?.[0]?.clients?.first_name} {account.client_sb_accounts?.[0]?.clients?.last_name}
                            </div>
                            <div className="text-sm text-gray-500">{account.client_sb_accounts?.[0]?.clients?.mobile_number}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-mono text-sm">{account.sb_account_number}</div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getTypeColor(account.account_type)}>
                            {account.account_type}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge className={account.opened_by_agency ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}>
                            {account.opened_by_agency ? 'Agency' : 'Non-Agency'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium text-green-600">
                            ₹{(account.opening_balance || 0).toLocaleString()}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(account.status)}>
                            {account.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {formatDisplayDate(account.created_at)}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => navigate(`/accounts/${account.id}`)}
                              className="h-8 w-8 p-0"
                              title="View Details"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => navigate(`/accounts/${account.id}/edit`)}
                              className="h-8 w-8 p-0"
                              title="Edit Account"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDelete(account.id)}
                              className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                              title="Delete Account"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="p-4 border-t">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-600">
                      Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, totalCount)} of {totalCount} results
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="flex items-center gap-1"
                      >
                        <ChevronLeft className="h-4 w-4" />
                        Previous
                      </Button>

                      <div className="flex items-center gap-1">
                        {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                          let page: number;
                          if (totalPages <= 5) {
                            page = i + 1;
                          } else if (currentPage <= 3) {
                            page = i + 1;
                          } else if (currentPage >= totalPages - 2) {
                            page = totalPages - 4 + i;
                          } else {
                            page = currentPage - 2 + i;
                          }

                          return (
                            <Button
                              key={page}
                              variant={currentPage === page ? "default" : "outline"}
                              size="sm"
                              onClick={() => handlePageChange(page)}
                              className="w-10 h-10 p-0"
                            >
                              {page}
                            </Button>
                          );
                        })}
                      </div>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className="flex items-center gap-1"
                      >
                        Next
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-12">
              <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No accounts found</h3>
              <p className="text-gray-500 mb-4">
                {searchTerm || statusFilter !== 'all' || typeFilter !== 'all'
                  ? 'Try adjusting your search or filters'
                  : 'Get started by adding your first account'
                }
              </p>
              {!searchTerm && statusFilter === 'all' && typeFilter === 'all' && dateFilter === 'all' && (
                <Button onClick={() => navigate('/accounts/new')}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Account
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Accounts;